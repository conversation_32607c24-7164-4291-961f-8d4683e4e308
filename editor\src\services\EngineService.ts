/**
 * 引擎服务
 * 负责连接编辑器与DL（Digital Learning）引擎
 */
import {
  Engine,
  EngineOptions,
  Scene,
  Camera,
  Vector3,
  Entity,
  Transform
} from '../libs/dl-engine.mjs';

// 引擎事件类型
export enum EngineEventType {
  INITIALIZED = 'initialized',
  SCENE_LOADED = 'sceneLoaded',
  SCENE_UNLOADED = 'sceneUnloaded',
  OBJECT_SELECTED = 'objectSelected',
  OBJECT_DESELECTED = 'objectDeselected',
  OBJECT_ADDED = 'objectAdded',
  OBJECT_REMOVED = 'objectRemoved',
  OBJECT_CHANGED = 'objectChanged',
  TRANSFORM_CHANGED = 'transformChanged',
  COMPONENT_ADDED = 'componentAdded',
  COMPONENT_REMOVED = 'componentRemoved',
  COMPONENT_CHANGED = 'componentChanged',
  RENDER_FRAME = 'renderFrame',
}

// 选择模式
export enum SelectionMode {
  SINGLE = 'single',
  MULTIPLE = 'multiple',
  ADD = 'add',
  SUBTRACT = 'subtract',
}

// 变换模式
export enum TransformMode {
  TRANSLATE = 'translate',
  ROTATE = 'rotate',
  SCALE = 'scale',
}

// 坐标空间
export enum TransformSpace {
  LOCAL = 'local',
  WORLD = 'world',
}

// 引擎服务类
class EngineService {
  private static instance: EngineService;

  private engine: Engine | null = null;
  private activeScene: Scene | null = null;
  private activeCamera: Camera | null = null;
  private selectedEntities: Entity[] = [];
  private transformMode: TransformMode = TransformMode.TRANSLATE;
  private transformSpace: TransformSpace = TransformSpace.LOCAL;

  // 事件系统
  private eventListeners: Map<string, Function[]> = new Map();

  private constructor() {
    // 初始化
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error('Error in engine service event listener:', error);
        }
      });
    }
  }

  /**
   * 移除所有事件监听器
   */
  private removeAllListeners(): void {
    this.eventListeners.clear();
  }

  /**
   * 获取引擎服务实例
   */
  public static getInstance(): EngineService {
    if (!EngineService.instance) {
      EngineService.instance = new EngineService();
    }
    return EngineService.instance;
  }

  /**
   * 初始化引擎
   * @param canvas 画布元素
   * @param options 引擎选项
   */
  public async initialize(canvas: HTMLCanvasElement, options: Partial<EngineOptions> = {}): Promise<void> {
    if (this.engine) {
      return;
    }

    try {
      // 创建引擎实例
      this.engine = new Engine({
        canvas,
        autoStart: false,
        ...options,
      });

      // 初始化引擎
      this.engine.initialize();

      // 发出初始化事件
      this.emit(EngineEventType.INITIALIZED, this.engine);

      console.log('引擎初始化成功');
    } catch (error) {
      console.error('引擎初始化失败:', error);
      throw error;
    }
  }

  /**
   * 启动引擎
   */
  public start(): void {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    this.engine.start();
  }

  /**
   * 停止引擎
   */
  public stop(): void {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    this.engine.stop();
  }

  /**
   * 加载场景
   * @param sceneData 场景数据
   */
  public async loadScene(sceneData: any): Promise<Scene> {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    try {
      // 卸载当前场景
      if (this.activeScene) {
        await this.unloadScene();
      }

      // 创建新场景
      const world = this.engine.getWorld();
      this.activeScene = world.createScene();

      // 从数据加载场景
      if (sceneData) {
        await this.activeScene.deserialize(sceneData);
      }

      // 设置活动相机
      const cameras = this.activeScene.getComponentsOfType<Camera>('Camera');
      if (cameras.length > 0) {
        this.activeCamera = cameras[0];
      } else {
        // 创建默认相机
        const cameraEntity = this.activeScene.createEntity('Main Camera');
        const transform = cameraEntity.getComponent<Transform>('Transform');
        transform.setPosition(new Vector3(0, 5, 10));
        transform.lookAt(new Vector3(0, 0, 0));

        this.activeCamera = cameraEntity.addComponent<Camera>('Camera');
      }

      // 发出场景加载事件
      this.emit(EngineEventType.SCENE_LOADED, this.activeScene);

      console.log('场景加载成功');
      return this.activeScene;
    } catch (error) {
      console.error('场景加载失败:', error);
      throw error;
    }
  }

  /**
   * 卸载当前场景
   */
  public async unloadScene(): Promise<void> {
    if (!this.engine || !this.activeScene) {
      return;
    }

    try {
      // 清除选中的实体
      this.clearSelection();

      // 发出场景卸载事件
      this.emit(EngineEventType.SCENE_UNLOADED, this.activeScene);

      // 销毁场景
      this.activeScene.dispose();
      this.activeScene = null;
      this.activeCamera = null;

      console.log('场景卸载成功');
    } catch (error) {
      console.error('场景卸载失败:', error);
      throw error;
    }
  }

  /**
   * 保存当前场景
   * @returns 场景数据
   */
  public async saveScene(): Promise<any> {
    if (!this.engine || !this.activeScene) {
      throw new Error('没有活动场景');
    }

    try {
      // 序列化场景
      const sceneData = await this.activeScene.serialize();
      console.log('场景保存成功');
      return sceneData;
    } catch (error) {
      console.error('场景保存失败:', error);
      throw error;
    }
  }

  /**
   * 选择实体
   * @param entity 实体
   * @param mode 选择模式
   */
  public selectEntity(entity: Entity, mode: SelectionMode = SelectionMode.SINGLE): void {
    if (!entity) {
      return;
    }

    switch (mode) {
      case SelectionMode.SINGLE:
        // 清除当前选择
        this.clearSelection();
        // 添加新选择
        this.selectedEntities.push(entity);
        this.emit(EngineEventType.OBJECT_SELECTED, entity);
        break;

      case SelectionMode.MULTIPLE:
      case SelectionMode.ADD:
        // 如果实体不在选择列表中，添加它
        if (!this.selectedEntities.includes(entity)) {
          this.selectedEntities.push(entity);
          this.emit(EngineEventType.OBJECT_SELECTED, entity);
        }
        break;

      case SelectionMode.SUBTRACT:
        // 从选择列表中移除实体
        const index = this.selectedEntities.indexOf(entity);
        if (index !== -1) {
          this.selectedEntities.splice(index, 1);
          this.emit(EngineEventType.OBJECT_DESELECTED, entity);
        }
        break;
    }
  }

  /**
   * 取消选择实体
   * @param entity 实体，如果为空则取消所有选择
   */
  public deselectEntity(entity?: Entity): void {
    if (!entity) {
      this.clearSelection();
      return;
    }

    const index = this.selectedEntities.indexOf(entity);
    if (index !== -1) {
      this.selectedEntities.splice(index, 1);
      this.emit(EngineEventType.OBJECT_DESELECTED, entity);
    }
  }

  /**
   * 清除所有选择
   */
  public clearSelection(): void {
    const entities = [...this.selectedEntities];
    this.selectedEntities = [];

    for (const entity of entities) {
      this.emit(EngineEventType.OBJECT_DESELECTED, entity);
    }
  }

  /**
   * 获取选中的实体
   */
  public getSelectedEntities(): Entity[] {
    return [...this.selectedEntities];
  }

  /**
   * 设置变换模式
   * @param mode 变换模式
   */
  public setTransformMode(mode: TransformMode): void {
    this.transformMode = mode;
  }

  /**
   * 获取变换模式
   */
  public getTransformMode(): TransformMode {
    return this.transformMode;
  }

  /**
   * 设置变换空间
   * @param space 变换空间
   */
  public setTransformSpace(space: TransformSpace): void {
    this.transformSpace = space;
  }

  /**
   * 获取变换空间
   */
  public getTransformSpace(): TransformSpace {
    return this.transformSpace;
  }

  /**
   * 创建实体
   * @param name 实体名称
   * @param parent 父实体
   */
  public createEntity(name: string, parent?: Entity): Entity {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    const entity = this.activeScene.createEntity(name, parent);
    this.emit(EngineEventType.OBJECT_ADDED, entity);
    return entity;
  }

  /**
   * 删除实体
   * @param entity 实体
   */
  public removeEntity(entity: Entity): void {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    // 如果实体被选中，取消选择
    this.deselectEntity(entity);

    // 删除实体
    this.activeScene.removeEntity(entity);
    this.emit(EngineEventType.OBJECT_REMOVED, entity);
  }

  /**
   * 获取引擎实例
   */
  public getEngine(): Engine | null {
    return this.engine;
  }

  /**
   * 获取活动场景
   */
  public getActiveScene(): Scene | null {
    return this.activeScene;
  }

  /**
   * 获取活动相机
   */
  public getActiveCamera(): Camera | null {
    return this.activeCamera;
  }

  /**
   * 设置活动相机
   * @param camera 相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }

  /**
   * 销毁引擎服务
   */
  public dispose(): void {
    if (this.engine) {
      this.engine.dispose();
      this.engine = null;
    }

    this.activeScene = null;
    this.activeCamera = null;
    this.selectedEntities = [];

    this.removeAllListeners();
  }

  /**
   * 调用引擎方法
   * @param method 方法名称
   * @param args 参数
   * @returns 返回值
   */
  public async callEngineMethod(method: string, ...args: any[]): Promise<any> {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    try {
      // 这里应该是实际调用引擎方法的代码
      // 目前只是一个模拟实现
      console.log(`调用引擎方法: ${method}`, args);

      // 模拟一些方法的返回值
      if (method === 'getAvailableAnimationClips') {
        return ['idle', 'walk', 'run', 'jump', 'attack', 'death'];
      } else if (method === 'getStateMachineList') {
        return ['主状态机', '战斗状态机', '移动状态机'];
      } else if (method === 'loadStateMachine') {
        return {
          states: [
            {
              name: '空闲',
              type: 'SingleAnimationState',
              clipName: 'idle',
              loop: true,
              clamp: false,
              position: { x: 100, y: 100 }
            },
            {
              name: '行走',
              type: 'SingleAnimationState',
              clipName: 'walk',
              loop: true,
              clamp: false,
              position: { x: 300, y: 100 }
            },
            {
              name: '跑步',
              type: 'SingleAnimationState',
              clipName: 'run',
              loop: true,
              clamp: false,
              position: { x: 500, y: 100 }
            }
          ],
          transitions: [
            {
              from: '空闲',
              to: '行走',
              conditionExpression: 'stateMachine.getParameter("speed") > 0.1',
              duration: 0.3,
              canInterrupt: true
            },
            {
              from: '行走',
              to: '空闲',
              conditionExpression: 'stateMachine.getParameter("speed") <= 0.1',
              duration: 0.3,
              canInterrupt: true
            },
            {
              from: '行走',
              to: '跑步',
              conditionExpression: 'stateMachine.getParameter("speed") > 1.0',
              duration: 0.3,
              canInterrupt: true
            },
            {
              from: '跑步',
              to: '行走',
              conditionExpression: 'stateMachine.getParameter("speed") <= 1.0',
              duration: 0.3,
              canInterrupt: true
            }
          ],
          parameters: [
            {
              name: 'speed',
              type: 'number',
              defaultValue: 0,
              minValue: 0,
              maxValue: 10
            },
            {
              name: 'isJumping',
              type: 'boolean',
              defaultValue: false
            }
          ],
          currentState: '空闲'
        };
      } else if (method === 'createStateMachine') {
        return {
          name: '新状态机',
          states: [
            {
              name: '空闲',
              type: 'SingleAnimationState',
              clipName: 'idle',
              loop: true,
              clamp: false,
              position: { x: 100, y: 100 }
            }
          ],
          transitions: [],
          parameters: [],
          currentState: '空闲'
        };
      } else if (method === 'getStateMachineDebugInfo') {
        return {
          events: [
            {
              type: 'stateEnter',
              time: 0,
              data: { state: { name: '空闲', type: 'SingleAnimationState' } }
            },
            {
              type: 'parameterChange',
              time: 1.5,
              data: { name: 'speed', oldValue: 0, newValue: 0.5 }
            },
            {
              type: 'conditionEvaluate',
              time: 1.5,
              data: { expression: 'stateMachine.getParameter("speed") > 0.1', result: true }
            },
            {
              type: 'transitionStart',
              time: 1.5,
              data: { transition: { from: '空闲', to: '行走' } }
            },
            {
              type: 'stateExit',
              time: 1.8,
              data: { state: { name: '空闲', type: 'SingleAnimationState' } }
            },
            {
              type: 'stateEnter',
              time: 1.8,
              data: { state: { name: '行走', type: 'SingleAnimationState' } }
            },
            {
              type: 'transitionEnd',
              time: 1.8,
              data: { transition: { from: '空闲', to: '行走' } }
            }
          ],
          parameters: [
            { name: 'speed', type: 'number', value: 0.5 },
            { name: 'isJumping', type: 'boolean', value: false }
          ],
          currentState: {
            name: '行走',
            type: 'SingleAnimationState',
            clipName: 'walk',
            loop: true,
            clamp: false
          }
        };
      }

      // 默认返回成功
      return true;
    } catch (error) {
      console.error(`调用引擎方法失败: ${method}`, error);
      throw error;
    }
  }
}

export default EngineService.getInstance();
